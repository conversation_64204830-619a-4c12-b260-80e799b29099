# 作业一  数据预处理

## 作业目的

理解数据质量的关键属性，实践修复数据中存在的质量问题并掌握基本的数据分析技巧。

## 作业内容

### 数据预处理学习

了解常见的数据预处理手段，例如数据插值，数据标准化/归一化,文本数据分词等等，并将学习成果罗列出来

### 实战

Netflix 是最受欢迎的媒体和视频流媒体平台之一。他们的平台上有超 8000 部电影或电视节目，截至 2021 年年中，他们在全球拥有超过 2亿 订阅者。我们提取出了2008年至 2021 年 Netflix 的新增内容，具体包含 Netflix 上所有电影和电视节目的列表，以及演员、导演、收视率、发行年份、持续时间等详细信息。数据集的数据列说明如下:

- show_id: 每部作品的唯一 ID
- type: 标识符 - 电影或电视秀
- title: 作品标题
- director: 作品导演
- cast: 参与作品的演员
- data_added: 作品在 Netflix 平台上被添加的日期
- release_year: 作品的实际发行年份
- rating: 作品的分级
- duration: 作品总时长(分钟数或季数)
- listed_in: 作品细分类
- description: 作品描述

请参考课程中提到的 **Accurate**, **Complete**, **Unique**, **Up-to-date**, **Consistent** 五个数据质量分析维度，分析该数据集存在哪些数据质量问题，并通过数据预处理修复这些问题。

- Accurate：数据的值应当真实反映现实世界的情况。例如电影时长，上映年份是否合理等。
- Complete：衡量所必须的数据的完整程度，如不能缺失的空值检查。
- Unique：数据集中每个记录应唯一，避免重复项，唯一性确保数据没有冗余。
- Up-to-date：数据应为最新的，反映当前状态。
- Consistent： 数据元素的类型和含义必须一致和清晰，如所有日期格式都一致吗。

这是一些参考角度：

- 针对country, director等可能存在多个冗余词条的文本数据列，考虑只保留最前面一条
- 针对空数据较多的director等列，考虑基于与该数据行关联的其他数据行的数据(如导演相同), 对数据进行补全

数据处理完毕后，需要进行数据可视化，例如:

- 所有作品的type，country的分布
- Netflix历年新增的作品数
- 顶级导演与演员(按照导演的作品数降序排序呈现，并注明导演的电视节目数与电影数)

## 作业提交

- 本次作业建议同学们使用 [Jupyter Notebook ](https://jupyter.org/) 完成。

- 作业文档打包提交，需要包括数据预处理学习、数据质量分析内容与数据可视化截图
